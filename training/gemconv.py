#!/usr/bin/env python3
import json
import argparse
import os
import re

# --- Constants for simplified format delimiters ---
# For legacy PaLM 2 models
INSTRUCTION_PROMPT_TAG = "[PROMPT]"
INSTRUCTION_RESPONSE_TAG = "[RESPONSE]"

# For modern Gemini models
CHAT_USER_TAG = "[USER]"
CHAT_MODEL_TAG = "[MODEL]"

RECORD_SEPARATOR = "---"

def create_simplified_data_file_examples():
    """Creates example simplified data files if they don't exist."""
    chat_example = f"""
# This is an example file for the Gemini 'chat' format.
# Each record can contain multiple turns of a conversation.
# Use '{CHAT_USER_TAG}' for user messages and '{CHAT_MODEL_TAG}' for model responses.
# Use '{RECORD_SEPARATOR}' to separate entire conversations.

{CHAT_USER_TAG}
Hi, can you help me plan a trip?

{CHAT_MODEL_TAG}
Of course! Where are you thinking of going?

{RECORD_SEPARATOR}

{CHAT_USER_TAG}
What is the fastest land animal?

{CHAT_MODEL_TAG}
The cheetah is the fastest land animal, capable of reaching speeds up to 75 mph (120 km/h) in short bursts.
"""
    if not os.path.exists("example_gemini_data.txt"):
        with open("example_gemini_data.txt", "w", encoding="utf-8") as f:
            f.write(chat_example.strip())
        print("Created 'example_gemini_data.txt'.")


def parse_instruction_data(text_content):
    """
    Parses text for instruction-tuning data. (Legacy format for PaLM 2)
    Yields JSON objects in the format: {"input_text": "...", "output_text": "..."}
    """
    normalized_content = text_content.replace('\u00A0', ' ').replace('\u200B', '')
    records = normalized_content.split(RECORD_SEPARATOR)
    
    for i, record in enumerate(records):
        record = record.strip()
        if not record:
            continue

        prompt_search_str = re.escape(INSTRUCTION_PROMPT_TAG)
        response_search_str = re.escape(INSTRUCTION_RESPONSE_TAG)
        
        prompt_match = re.search(f"{prompt_search_str}(.*?)(?={response_search_str}|$)", record, re.DOTALL)
        response_match = re.search(f"{response_search_str}(.*)", record, re.DOTALL)

        if not prompt_match or not response_match:
            print(f"Warning: Skipping malformed instruction record #{i+1}. Could not find both tags.")
            continue

        prompt = prompt_match.group(1).strip()
        response = response_match.group(1).strip()

        if not prompt or not response:
            print(f"Warning: Skipping empty instruction record #{i+1}. Prompt or response is empty.")
            continue

        yield {"input_text": prompt, "output_text": response}


def parse_chat_data(text_content):
    """
    Parses text for chat-tuning data. (Modern format for Gemini)
    Yields JSON objects in the format: {"contents": [{"role": "...", "parts": [{"text": "..."}]}, ...]}
    """
    # Normalize non-breaking spaces and other potential unicode whitespace
    normalized_content = text_content.replace('\u00A0', ' ').replace('\u200B', '')
    conversations = normalized_content.split(RECORD_SEPARATOR)
    
    for i, conv in enumerate(conversations):
        conv = conv.strip()
        if not conv:
            continue
        
        user_tag_s = re.escape(CHAT_USER_TAG)
        model_tag_s = re.escape(CHAT_MODEL_TAG)

        pattern = re.compile(f"({user_tag_s}|{model_tag_s})\\s*(.*?)(?={user_tag_s}|{model_tag_s}|$)", re.DOTALL)
        matches = pattern.findall(conv)

        if not matches:
            print(f"Warning: Skipping conversation #{i+1}. No tags found.")
            print(f"         Problem snippet: {repr(conv[:100])}...")
            continue
        
        # --- FORMAT FIX: Changed from 'messages' to 'contents' structure ---
        contents = []
        for tag, content in matches:
            # The API uses "role" not "author"
            role = "user" if tag == CHAT_USER_TAG else "model"
            cleaned_content = content.strip()
            if cleaned_content:
                # The API uses a "parts" array containing a "text" object
                turn = {"role": role, "parts": [{"text": cleaned_content}]}
                contents.append(turn)

        if contents:
            # The top-level key must be "contents"
            yield {"contents": contents}
        else:
             print(f"Warning: Skipping conversation #{i+1} as it contained no message content after cleaning.")


def main():
    """Main function to parse arguments and run the conversion."""
    parser = argparse.ArgumentParser(
        description="Convert simplified text files to JSONL for Vertex AI Gemini tuning.",
        formatter_class=argparse.RawTextHelpFormatter
    )

    parser.add_argument("input_file", help="Path to the simplified input text file.")
    parser.add_argument("output_file", help="Path for the output JSONL file.")
    parser.add_argument(
        "--format",
        choices=['instruction', 'chat'],
        default='chat',
        help="The format of the training data. 'chat' is for Gemini. 'instruction' is for legacy PaLM 2 models. Default is 'chat'."
    )
    parser.add_argument(
        '--create-examples',
        action='store_true',
        help="If specified, creates an example data file ('example_gemini_data.txt') and exits."
    )

    args = parser.parse_args()

    if args.create_examples:
        create_simplified_data_file_examples()
        return

    try:
        with open(args.input_file, "r", encoding="utf-8") as f:
            content = f.read()
    except FileNotFoundError:
        print(f"Error: Input file not found at '{args.input_file}'")
        return
    except Exception as e:
        print(f"Error reading input file: {e}")
        return

    print(f"Reading data from '{args.input_file}'...")
    print(f"Processing as '{args.format}' format...")
    
    parser_func = parse_chat_data if args.format == 'chat' else parse_instruction_data

    processed_records = 0
    try:
        with open(args.output_file, "w", encoding="utf-8") as f:
            for json_record in parser_func(content):
                f.write(json.dumps(json_record) + "\n")
                processed_records += 1
    except Exception as e:
        print(f"An error occurred during file writing: {e}")
        return

    if processed_records > 0:
        print(f"\n✅ Success! Wrote {processed_records} records to '{args.output_file}'")
    else:
        print(f"\n⚠️ Conversion finished, but 0 records were written to '{args.output_file}'.")
        print("   Please check the warning messages above to diagnose issues in your input file.")

if __name__ == "__main__":
    main()
